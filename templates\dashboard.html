{% extends "layout.html" %}
{% block title %}Dashboard - {{ stats.username }}{% endblock %}

{% block content %}
<div class="container">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary text-white p-4 rounded-4">
                <h1 class="display-6 fw-bold mb-2">
                    <i class="fas fa-tachometer-alt"></i> Welcome, {{ stats.username }}!
                </h1>
                <p class="lead mb-0">
                    {% if stats.is_premium %}
                        <span class="badge bg-warning"><i class="fas fa-crown"></i> Premium Member</span>
                    {% else %}
                        <span class="badge bg-light text-dark"><i class="fas fa-user"></i> Standard Member</span>
                    {% endif %}
                    Member since {{ stats.member_since.strftime('%B %Y') }}
                </p>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <div class="text-primary mb-2"><i class="fas fa-upload fa-2x"></i></div>
                    <h3 class="text-primary">{{ stats.total_uploads }}</h3>
                    <p class="card-text">Total Uploads</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <div class="text-success mb-2"><i class="fas fa-plus-circle fa-2x"></i></div>
                    <h3 class="text-success">{{ stats.remaining_uploads }}</h3>
                    <p class="card-text">Remaining</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <div class="text-info mb-2"><i class="fas fa-limit fa-2x"></i></div>
                    <h3 class="text-info">{{ stats.max_uploads }}</h3>
                    <p class="card-text">Max Allowed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <div class="text-warning mb-2"><i class="fas fa-clock fa-2x"></i></div>
                    <h6 class="text-warning">
                        {% if stats.last_upload %}
                            {{ stats.last_upload.strftime('%m/%d') }}
                        {% else %}
                            Never
                        {% endif %}
                    </h6>
                    <p class="card-text">Last Upload</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-upload"></i><br>
                                Upload Archives
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('reset_session') }}" class="btn btn-outline-warning btn-lg w-100">
                                <i class="fas fa-refresh"></i><br>
                                Reset Session
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-sign-out-alt"></i><br>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-cog"></i> Account Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Username:</strong></td>
                            <td>{{ stats.username }}</td>
                        </tr>
                        <tr>
                            <td><strong>Account Type:</strong></td>
                            <td>
                                {% if stats.is_premium %}
                                    <span class="badge bg-warning"><i class="fas fa-crown"></i> Premium</span>
                                {% else %}
                                    <span class="badge bg-secondary">Standard</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Member Since:</strong></td>
                            <td>{{ stats.member_since.strftime('%B %d, %Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Upload Limit:</strong></td>
                            <td>{{ stats.max_uploads }} archives per account</td>
                        </tr>
                        <tr>
                            <td><strong>Last Activity:</strong></td>
                            <td>
                                {% if stats.last_upload %}
                                    {{ stats.last_upload.strftime('%B %d, %Y at %I:%M %p') }}
                                {% else %}
                                    No uploads yet
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Usage Stats</h5>
                </div>
                <div class="card-body text-center">
                    <!-- Progress Bar -->
                    <div class="mb-3">
                        {% set usage_percent = ((stats.total_uploads / stats.max_uploads) * 100)|round %}
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar 
                                {% if usage_percent >= 90 %}bg-danger
                                {% elif usage_percent >= 70 %}bg-warning
                                {% else %}bg-success{% endif %}" 
                                role="progressbar" 
                                style="width: {{ usage_percent }}%"
                                aria-valuenow="{{ usage_percent }}" 
                                aria-valuemin="0" 
                                aria-valuemax="100">
                                {{ usage_percent }}%
                            </div>
                        </div>
                        <small class="text-muted">{{ stats.total_uploads }} of {{ stats.max_uploads }} uploads used</small>
                    </div>

                    <!-- Status -->
                    {% if stats.remaining_uploads > 0 %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i><br>
                            <strong>{{ stats.remaining_uploads }}</strong> uploads remaining
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i><br>
                            Upload limit reached
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Features Info -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5><i class="fas fa-star text-warning"></i> Your Benefits</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> {{ stats.max_uploads }} archive uploads</li>
                                <li><i class="fas fa-check text-success"></i> ZIP & RAR support</li>
                                <li><i class="fas fa-check text-success"></i> WebP conversion</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Auto file cleanup</li>
                                <li><i class="fas fa-check text-success"></i> Upload history tracking</li>
                                <li><i class="fas fa-check text-success"></i> No watermarks</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
