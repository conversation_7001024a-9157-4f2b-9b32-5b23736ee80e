﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON>hire<PERSON>'s ImagePro Studio{% endblock %}</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
      .navbar-brand { font-weight: bold; font-size: 1.5rem; }
      .footer { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      body { min-height: 100vh; display: flex; flex-direction: column; }
      main { flex: 1; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('index') }}">
          <i class="fas fa-image"></i> Dhiren's ImagePro Studio
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('index') }}">
                <i class="fas fa-home"></i> Home
              </a>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="converterDropdown" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-magic"></i> Converters
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('index') }}">
                  <i class="fas fa-archive"></i> Archive Converter
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('single_image_convert') }}">
                  <i class="fas fa-image"></i> Single Image
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('batch_images') }}">
                  <i class="fas fa-images"></i> Batch Images
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('bg_remover') }}">
                  <i class="fas fa-magic"></i> AI Background Remover
                </a></li>
              </ul>
            </li>
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> Dashboard
              </a>
            </li>
            {% endif %}
          </ul>
          
          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                  <i class="fas fa-user"></i> {{ current_user.username }}
                </a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                  </a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="{{ url_for('reset_session') }}">
                    <i class="fas fa-refresh"></i> Reset Session
                  </a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                  </a></li>
                </ul>
              </li>
            {% else %}
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('login') }}">
                  <i class="fas fa-sign-in-alt"></i> Login
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('register') }}">
                  <i class="fas fa-user-plus"></i> Register
                </a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="container mt-3">
          {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}

    <main class="py-4">
      {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
