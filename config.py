import os

class Config:
    # <PERSON>hire<PERSON>'s Image Converter Pro - Advanced Configuration
    SECRET_KEY = 'dhiren-image-converter-pro-2024-advanced-secret'
    
    # Database configuration
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{os.path.join(BASE_DIR, "instance", "dhiren_image_converter.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # File upload settings - Enhanced limits
    UPLOAD_FOLDER = 'static/uploads'
    COMPRESSED_FOLDER = 'static/compressed'
    SINGLE_IMAGE_FOLDER = 'static/single_images'
    BATCH_IMAGES_FOLDER = 'static/batch_images'
    BG_REMOVED_FOLDER = 'static/bg_removed'
    MAX_CONTENT_LENGTH = 200 * 1024 * 1024  # 200MB max file size for registered users
    MAX_GUEST_FILE_SIZE = 50 * 1024 * 1024   # 50MB for guests
    
    # Plan-based Upload Limits System
    # Free Plan (Guest)
    GUEST_MAX_UPLOADS = 3
    GUEST_MAX_BATCH_IMAGES = 3
    GUEST_MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    # Basic Plan (Free Registered Users - Dhiren ke liye free!)
    BASIC_MAX_UPLOADS = 50
    BASIC_MAX_BATCH_IMAGES = 10
    BASIC_MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    # Pro Plan (Would be paid but free for you!)
    PRO_MAX_UPLOADS = 200
    PRO_MAX_BATCH_IMAGES = 25
    PRO_MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    # Premium Plan (Ultimate - free for you!)
    PREMIUM_MAX_UPLOADS = 999
    PREMIUM_MAX_BATCH_IMAGES = 50
    PREMIUM_MAX_FILE_SIZE = 200 * 1024 * 1024  # 200MB
    
    # Legacy settings for backward compatibility
    MAX_GUEST_UPLOADS = GUEST_MAX_UPLOADS
    MAX_REGISTERED_UPLOADS = BASIC_MAX_UPLOADS
    MAX_SINGLE_IMAGE_UPLOADS = BASIC_MAX_BATCH_IMAGES
    
    # File processing settings
    ALLOWED_ARCHIVE_EXTENSIONS = {'.zip', '.rar', '.7z', '.tar', '.gz'}  # Added more formats
    ALLOWED_IMAGE_EXTENSIONS = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', 
        '.svg', '.ico', '.psd', '.raw', '.cr2', '.nef', '.dng', '.heic', '.avif'
    }  # Enhanced image format support
    
    # Output format options
    SUPPORTED_OUTPUT_FORMATS = ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff']
    DEFAULT_OUTPUT_FORMAT = 'webp'
    
    # WebP conversion settings - Advanced options
    WEBP_QUALITY = 85  # High quality WebP
    WEBP_LOSSLESS = False
    
    # Advanced image processing settings
    IMAGE_RESIZE_OPTIONS = {
        'thumbnail': (150, 150),
        'small': (400, 400),
        'medium': (800, 800),
        'large': (1200, 1200),
        'xl': (1920, 1920),
        'original': None
    }
    
    # Compression quality settings
    QUALITY_SETTINGS = {
        'low': 60,
        'medium': 75,
        'high': 85,
        'ultra': 95
    }
    
    # File cleanup settings
    AUTO_DELETE_AFTER = 60  # Files automatically delete after 60 minutes
    
    # Folder naming settings
    SANITIZE_FILENAMES = True
    REPLACE_SPACES = True
    
    # Advanced features
    ENABLE_BATCH_PROCESSING = True
    ENABLE_IMAGE_OPTIMIZATION = True
    ENABLE_METADATA_REMOVAL = True
    ENABLE_WATERMARKING = False  # Future feature
    ENABLE_BACKGROUND_REMOVAL = True
    ENABLE_MULTIPLE_IMAGE_UPLOAD = True
    
    # Background Removal AI Models
    BG_REMOVAL_MODELS = {
        'u2net': 'General Purpose (Fast)',
        'u2netp': 'Lightweight (Faster)',
        'silueta': 'People Focus (Accurate)',
        'isnet-general-use': 'High Quality (Slower)',
        'sam': 'Segment Anything (Advanced)'
    }
    DEFAULT_BG_MODEL = 'u2net'
    
    # Plan System Configuration
    PLANS = {
        'guest': {
            'name': 'Free Guest',
            'max_uploads': GUEST_MAX_UPLOADS,
            'max_batch_images': GUEST_MAX_BATCH_IMAGES,
            'max_file_size': GUEST_MAX_FILE_SIZE,
            'features': ['basic_conversion', 'single_image'],
            'price': 'Free',
            'color': 'secondary'
        },
        'basic': {
            'name': 'Basic Plan',
            'max_uploads': BASIC_MAX_UPLOADS,
            'max_batch_images': BASIC_MAX_BATCH_IMAGES,
            'max_file_size': BASIC_MAX_FILE_SIZE,
            'features': ['basic_conversion', 'single_image', 'batch_upload', 'bg_removal'],
            'price': 'Free for You!',
            'color': 'primary'
        },
        'pro': {
            'name': 'Pro Plan',
            'max_uploads': PRO_MAX_UPLOADS,
            'max_batch_images': PRO_MAX_BATCH_IMAGES,
            'max_file_size': PRO_MAX_FILE_SIZE,
            'features': ['basic_conversion', 'single_image', 'batch_upload', 'bg_removal', 'priority_processing'],
            'price': 'Free for You!',
            'color': 'success'
        },
        'premium': {
            'name': 'Premium Plan',
            'max_uploads': PREMIUM_MAX_UPLOADS,
            'max_batch_images': PREMIUM_MAX_BATCH_IMAGES,
            'max_file_size': PREMIUM_MAX_FILE_SIZE,
            'features': ['basic_conversion', 'single_image', 'batch_upload', 'bg_removal', 'priority_processing', 'api_access'],
            'price': 'Free for You!',
            'color': 'warning'
        }
    }
    
    # Branding
    APP_NAME = "Dhiren's ImagePro Studio"
    APP_TAGLINE = "Professional Image Conversion & Optimization Platform"
    APP_AUTHOR = "Dhiren"
    APP_VERSION = "2.0 Advanced"
    
    # Social links (for footer)
    SOCIAL_LINKS = {
        'github': '#',
        'linkedin': '#',
        'website': '#'
    }
