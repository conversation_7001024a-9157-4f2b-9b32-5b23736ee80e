{% extends "layout.html" %}
{% block title %}📦 Batch Image Converter - Convert Multiple Images{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center p-5 rounded-4 text-white mb-4" 
                 style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); box-shadow: 0 20px 40px rgba(240,147,251,0.3);">
                <div class="d-flex justify-content-center align-items-center mb-3">
                    <div class="bg-white rounded-circle p-3 me-3" style="box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                        <i class="fas fa-images text-primary fa-2x"></i>
                    </div>
                    <h1 class="display-4 fw-bold mb-0">Batch Image Converter</h1>
                </div>
                <p class="lead mb-4">Upload multiple images and convert them all at once - supports background removal too!</p>
                
                <!-- Plan Status -->
                {% if current_user.is_authenticated %}
                    {% if has_batch_feature %}
                        <div class="alert alert-success d-inline-block bg-white bg-opacity-20 border-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Batch Upload Active</strong> - Convert up to {{ max_batch_images }} images at once
                        </div>
                    {% else %}
                        <div class="alert alert-warning d-inline-block bg-white bg-opacity-20 border-0">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Upgrade Required</strong> - Batch upload needs Basic plan or higher
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info d-inline-block bg-white bg-opacity-20 border-0">
                        <i class="fas fa-user-plus me-2"></i>
                        <strong>Limited Access</strong> - Sign up for more batch upload capacity
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Upload Section -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header bg-gradient text-white text-center py-4" 
                     style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <h3 class="mb-0"><i class="fas fa-upload me-2"></i>Batch Image Upload & Conversion</h3>
                </div>
                
                <div class="card-body p-5">
                    {% if has_batch_feature or not current_user.is_authenticated %}
                    <form method="POST" enctype="multipart/form-data" id="batchForm" class="needs-validation" novalidate>
                        
                        <!-- Multiple File Upload -->
                        <div class="mb-4">
                            <label for="batch_images" class="form-label fw-bold fs-5">
                                <i class="fas fa-images text-primary me-2"></i>Select Multiple Images
                            </label>
                            <div class="upload-zone border-2 border-dashed border-primary rounded-4 p-4 text-center bg-light position-relative"
                                 style="min-height: 200px;">
                                <input type="file" class="form-control position-absolute w-100 h-100 opacity-0" 
                                       id="batch_images" name="batch_images" accept="image/*" multiple required 
                                       style="cursor: pointer;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5 class="text-primary">Drop images here or click to browse</h5>
                                    <p class="text-muted mb-2">Select up to <strong>{{ max_batch_images }}</strong> images</p>
                                    <p class="text-muted mb-0">Supports: JPG, PNG, WebP, GIF, BMP, TIFF</p>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Files Preview -->
                        <div id="filePreview" class="mb-4" style="display: none;">
                            <h6 class="fw-bold mb-3"><i class="fas fa-list me-2"></i>Selected Images</h6>
                            <div id="fileList" class="row g-3"></div>
                        </div>

                        <!-- Conversion Options Row -->
                        <div class="row g-4 mb-4">
                            <!-- Output Format -->
                            <div class="col-md-6">
                                <label for="output_format" class="form-label fw-bold">
                                    <i class="fas fa-file-export text-warning me-2"></i>Output Format
                                </label>
                                <select class="form-select form-select-lg" id="output_format" name="output_format">
                                    {% for format in supported_formats %}
                                    <option value="{{ format }}" {% if format == 'webp' %}selected{% endif %}>
                                        {{ format.upper() }}
                                        {% if format == 'webp' %} (Recommended){% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Quality Setting -->
                            <div class="col-md-6">
                                <label for="quality" class="form-label fw-bold">
                                    <i class="fas fa-medal text-success me-2"></i>Quality Level
                                </label>
                                <select class="form-select form-select-lg" id="quality" name="quality">
                                    {% for key, value in quality_settings.items() %}
                                    <option value="{{ key }}" {% if key == 'high' %}selected{% endif %}>
                                        {{ key.title() }} ({{ value }}%)
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- Advanced Options Row -->
                        <div class="row g-4 mb-4">
                            <!-- Resize Option -->
                            <div class="col-md-6">
                                <label for="resize_option" class="form-label fw-bold">
                                    <i class="fas fa-expand-arrows-alt text-info me-2"></i>Resize Images
                                </label>
                                <select class="form-select form-select-lg" id="resize_option" name="resize_option">
                                    <option value="original" selected>Keep Original Size</option>
                                    {% for key, value in resize_options.items() %}
                                    <option value="{{ key }}">{{ key.title() }} ({{ value[0] }}x{{ value[1] }})</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Background Removal -->
                            <div class="col-md-6">
                                {% if has_bg_feature %}
                                <label class="form-label fw-bold">
                                    <i class="fas fa-magic text-purple me-2"></i>Background Removal
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="remove_bg" name="remove_bg">
                                    <label class="form-check-label fw-bold" for="remove_bg">
                                        Remove backgrounds with AI
                                    </label>
                                </div>
                                <div id="bgModelSection" style="display: none;" class="mt-2">
                                    <select class="form-select" id="bg_model" name="bg_model">
                                        {% for model_key, model_name in bg_models.items() %}
                                        <option value="{{ model_key }}">{{ model_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                {% else %}
                                <label class="form-label fw-bold text-muted">
                                    <i class="fas fa-lock me-2"></i>Background Removal
                                </label>
                                <p class="text-muted mb-0">
                                    <small>Available in Basic plan and above</small>
                                </p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Upload Stats -->
                        <div class="alert alert-info rounded-4 border-0 mb-4" 
                             style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <strong class="text-primary">{{ remaining_uploads }}</strong><br>
                                    <small>Remaining Uploads</small>
                                </div>
                                <div class="col-md-3">
                                    <strong class="text-success">{{ max_batch_images }}</strong><br>
                                    <small>Max Batch Size</small>
                                </div>
                                <div class="col-md-3">
                                    <strong class="text-warning">Multiple</strong><br>
                                    <small>Formats Supported</small>
                                </div>
                                <div class="col-md-3">
                                    <strong class="text-info">ZIP</strong><br>
                                    <small>Download Archive</small>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-lg py-3 rounded-4 fw-bold" 
                                    style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; color: white; box-shadow: 0 10px 30px rgba(240,147,251,0.3);"
                                    id="convertBtn">
                                <i class="fas fa-cogs me-2"></i>Convert All Images
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <!-- Upgrade Required -->
                    <div class="text-center py-5">
                        <i class="fas fa-lock fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">Batch Upload Requires Upgrade</h4>
                        <p class="text-muted mb-4">This feature is available for Basic plan and above.</p>
                        <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg rounded-4">
                            <i class="fas fa-user-plus me-2"></i>Register for Free Access
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h2 class="fw-bold">⚡ Batch Processing Features</h2>
                <p class="lead text-muted">Process multiple images efficiently with advanced options</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3"><i class="fas fa-layer-group fa-3x"></i></div>
                            <h6 class="fw-bold">Batch Processing</h6>
                            <p class="text-muted small">Convert multiple images simultaneously</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-success mb-3"><i class="fas fa-magic fa-3x"></i></div>
                            <h6 class="fw-bold">AI Background Removal</h6>
                            <p class="text-muted small">Remove backgrounds from all images</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3"><i class="fas fa-compress-arrows-alt fa-3x"></i></div>
                            <h6 class="fw-bold">Smart Resizing</h6>
                            <p class="text-muted small">Resize all images to optimal dimensions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-info mb-3"><i class="fas fa-file-archive fa-3x"></i></div>
                            <h6 class="fw-bold">ZIP Download</h6>
                            <p class="text-muted small">Get all converted images in one archive</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 rounded-4">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                <h5 class="fw-bold mb-2">Processing Images...</h5>
                <p class="text-muted mb-0" id="modalText">Converting your images, please wait...</p>
                <div class="progress mt-3" style="height: 8px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-card {
    transition: all 0.3s ease;
}
.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.upload-zone:hover {
    border-color: #f093fb !important;
    background-color: #fef7ff !important;
}

.file-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.text-purple {
    color: #6f42c1 !important;
}
</style>

<script>
// Form submission with modal
document.getElementById('batchForm').addEventListener('submit', function() {
    var modal = new bootstrap.Modal(document.getElementById('processingModal'));
    modal.show();
});

// Background removal toggle
document.getElementById('remove_bg').addEventListener('change', function() {
    const bgModelSection = document.getElementById('bgModelSection');
    if (this.checked) {
        bgModelSection.style.display = 'block';
    } else {
        bgModelSection.style.display = 'none';
    }
});

// File selection and preview
document.getElementById('batch_images').addEventListener('change', function() {
    const files = Array.from(this.files);
    const maxFiles = {{ max_batch_images }};
    
    if (files.length > maxFiles) {
        alert(`Maximum ${maxFiles} images allowed. Please select fewer images.`);
        this.value = '';
        return;
    }
    
    if (files.length > 0) {
        displayFilePreview(files);
    }
});

function displayFilePreview(files) {
    const preview = document.getElementById('filePreview');
    const fileList = document.getElementById('fileList');
    
    fileList.innerHTML = '';
    
    files.forEach((file, index) => {
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        const fileDiv = document.createElement('div');
        fileDiv.className = 'col-md-6 col-lg-4';
        fileDiv.innerHTML = `
            <div class="file-preview">
                <div class="d-flex align-items-center">
                    <i class="fas fa-image text-primary me-2"></i>
                    <div class="flex-grow-1">
                        <small class="fw-bold">${file.name}</small><br>
                        <small class="text-muted">${fileSize} MB</small>
                    </div>
                    <i class="fas fa-check-circle text-success"></i>
                </div>
            </div>
        `;
        fileList.appendChild(fileDiv);
    });
    
    preview.style.display = 'block';
}

// Update convert button text based on format
document.getElementById('output_format').addEventListener('change', function() {
    const format = this.value.toUpperCase();
    const convertBtn = document.getElementById('convertBtn');
    convertBtn.innerHTML = `<i class="fas fa-cogs me-2"></i>Convert All to ${format}`;
});
</script>
{% endblock %} 