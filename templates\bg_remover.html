{% extends "layout.html" %}
{% block title %}🤖 AI Background Remover - Remove Backgrounds Instantly{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center p-5 rounded-4 text-white mb-4" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 20px 40px rgba(102,126,234,0.3);">
                <div class="d-flex justify-content-center align-items-center mb-3">
                    <div class="bg-white rounded-circle p-3 me-3" style="box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                        <i class="fas fa-magic text-primary fa-2x"></i>
                    </div>
                    <h1 class="display-4 fw-bold mb-0">AI Background Remover</h1>
                </div>
                <p class="lead mb-4">Remove backgrounds from any image using advanced AI models - instantly and precisely!</p>
                
                <!-- Feature Access Status -->
                {% if current_user.is_authenticated %}
                    {% if has_bg_feature %}
                        <div class="alert alert-success d-inline-block bg-white bg-opacity-20 border-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>AI Background Removal Enabled</strong> - Powered by advanced neural networks
                        </div>
                    {% else %}
                        <div class="alert alert-warning d-inline-block bg-white bg-opacity-20 border-0">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Upgrade Required</strong> - Background removal needs Basic plan or higher
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info d-inline-block bg-white bg-opacity-20 border-0">
                        <i class="fas fa-user-plus me-2"></i>
                        <strong>Registration Required</strong> - Sign up free to access AI background removal
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Upload Section -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header bg-gradient text-white text-center py-4" 
                     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h3 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Image for Background Removal</h3>
                </div>
                
                <div class="card-body p-5">
                    {% if has_bg_feature or not current_user.is_authenticated %}
                    <form method="POST" enctype="multipart/form-data" id="bgRemovalForm" class="needs-validation" novalidate>
                        
                        <!-- File Upload Section -->
                        <div class="mb-4">
                            <label for="bg_image" class="form-label fw-bold fs-5">
                                <i class="fas fa-image text-primary me-2"></i>Choose Image
                            </label>
                            <div class="upload-zone border-2 border-dashed border-primary rounded-4 p-4 text-center bg-light position-relative"
                                 style="min-height: 200px; transition: all 0.3s ease;">
                                <input type="file" class="form-control position-absolute w-100 h-100 opacity-0" 
                                       id="bg_image" name="bg_image" accept="image/*" required style="cursor: pointer;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5 class="text-primary">Drop your image here or click to browse</h5>
                                    <p class="text-muted mb-0">Supports: JPG, PNG, WebP, GIF, BMP, TIFF (Max 50MB)</p>
                                </div>
                            </div>
                            <div class="invalid-feedback">Please select an image file.</div>
                        </div>

                        <!-- AI Model Selection -->
                        <div class="mb-4">
                            <label for="bg_model" class="form-label fw-bold">
                                <i class="fas fa-brain text-success me-2"></i>AI Model Selection
                            </label>
                            <select class="form-select form-select-lg" id="bg_model" name="bg_model">
                                {% for model_key, model_name in bg_models.items() %}
                                <option value="{{ model_key }}" {% if model_key == 'u2net' %}selected{% endif %}>
                                    {{ model_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Output Format -->
                        <div class="mb-4">
                            <label for="output_format" class="form-label fw-bold">
                                <i class="fas fa-file-export text-warning me-2"></i>Output Format
                            </label>
                            <div class="row">
                                {% for format in supported_formats %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="output_format" 
                                               id="format_{{ format }}" value="{{ format }}" 
                                               {% if format == 'png' %}checked{% endif %}>
                                        <label class="form-check-label fw-bold" for="format_{{ format }}">
                                            {{ format.upper() }}
                                            {% if format == 'png' %}
                                            <span class="badge bg-success ms-1">Recommended</span>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Upload Stats -->
                        <div class="alert alert-info rounded-4 border-0 mb-4" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <strong class="text-primary">{{ remaining_uploads }}</strong><br>
                                    <small>Remaining Uploads</small>
                                </div>
                                <div class="col-md-4">
                                    <strong class="text-success">AI Powered</strong><br>
                                    <small>Background Removal</small>
                                </div>
                                <div class="col-md-4">
                                    <strong class="text-warning">Instant</strong><br>
                                    <small>Processing</small>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-lg py-3 rounded-4 fw-bold" 
                                    style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; box-shadow: 0 10px 30px rgba(102,126,234,0.3);"
                                    id="removeBtn">
                                <i class="fas fa-magic me-2"></i>Remove Background with AI
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <!-- Upgrade Required -->
                    <div class="text-center py-5">
                        <i class="fas fa-lock fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">Background Removal Requires Upgrade</h4>
                        <p class="text-muted mb-4">This feature is available for Basic plan and above.</p>
                        <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg rounded-4">
                            <i class="fas fa-user-plus me-2"></i>Register for Free Access
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h2 class="fw-bold">🚀 Advanced AI Features</h2>
                <p class="lead text-muted">Powered by state-of-the-art machine learning models</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3"><i class="fas fa-robot fa-3x"></i></div>
                            <h5 class="fw-bold">Multiple AI Models</h5>
                            <p class="text-muted">Choose from 5 different AI models optimized for different image types</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-success mb-3"><i class="fas fa-lightning-bolt fa-3x"></i></div>
                            <h5 class="fw-bold">Lightning Fast</h5>
                            <p class="text-muted">Process images in seconds with advanced GPU acceleration</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center hover-card">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3"><i class="fas fa-award fa-3x"></i></div>
                            <h5 class="fw-bold">Professional Quality</h5>
                            <p class="text-muted">Studio-grade results with precise edge detection</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 rounded-4">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                <h5 class="fw-bold mb-2">AI Processing in Progress...</h5>
                <p class="text-muted mb-0" id="modalText">Removing background using advanced neural networks...</p>
            </div>
        </div>
    </div>
</div>

<style>
.hover-card {
    transition: all 0.3s ease;
}
.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.upload-zone:hover {
    border-color: #667eea !important;
    background-color: #f8f9ff !important;
}
</style>

<script>
document.getElementById('bgRemovalForm').addEventListener('submit', function() {
    var modal = new bootstrap.Modal(document.getElementById('processingModal'));
    modal.show();
});

// File upload preview
document.getElementById('bg_image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const uploadContent = document.querySelector('.upload-content');
            uploadContent.innerHTML = `
                <img src="${e.target.result}" style="max-width: 100%; max-height: 150px; object-fit: contain;" class="mb-2 rounded">
                <h6 class="text-success"><i class="fas fa-check-circle me-1"></i>${file.name}</h6>
                <p class="text-muted mb-0">Ready for background removal</p>
            `;
        };
        reader.readAsDataURL(file);
    }
});
</script>
{% endblock %} 