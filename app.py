# -*- coding: utf-8 -*-
"""
Image Converter Web App - Complete Implementation
Features: Guest uploads, ZIP/RAR archives, WebP conversion, auto-cleanup
"""

from flask import Flask, render_template, redirect, url_for, request, flash, send_from_directory, session, after_this_request
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import inspect
from flask_login import LoginManager, login_user, logout_user, login_required, UserMixin, current_user
from flask_bcrypt import Bcrypt
from werkzeug.utils import secure_filename
from PIL import Image
import os
import uuid
import zipfile
import shutil
import re
import threading
import time
from datetime import datetime, timezone
from config import Config
from rembg import remove, new_session

app = Flask(__name__)
app.config.from_object(Config)

db = SQLAlchemy(app)
bcrypt = Bcrypt(app)

login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

# DATABASE MODELS
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_premium = db.Column(db.Boolean, default=False)
    plan = db.Column(db.String(50), default='basic')  # guest, basic, pro, premium
    
    def get_plan_info(self):
        """Get user's plan information"""
        plans = app.config.get('PLANS', {})
        return plans.get(self.plan, plans.get('basic', {}))
    
    def has_feature(self, feature):
        """Check if user has access to a specific feature"""
        plan_info = self.get_plan_info()
        return feature in plan_info.get('features', [])

class UploadSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    file_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_upload = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# UTILITY FUNCTIONS
def sanitize_filename(filename):
    """Filename sanitization with special character removal"""
    if app.config.get('SANITIZE_FILENAMES', True):
        filename = re.sub(r'[^\w\-_\.]', '', filename)
    if app.config.get('REPLACE_SPACES', True):
        filename = filename.replace(' ', '_')
    return filename

def is_allowed_archive(filename):
    """Check if file is allowed archive format"""
    allowed_extensions = app.config.get('ALLOWED_ARCHIVE_EXTENSIONS', {'.zip', '.rar'})
    return '.' in filename and os.path.splitext(filename)[1].lower() in allowed_extensions

def is_allowed_image(filename):
    """Check if file is allowed image format"""
    allowed_extensions = app.config.get('ALLOWED_IMAGE_EXTENSIONS', {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'})
    return '.' in filename and os.path.splitext(filename)[1].lower() in allowed_extensions

def extract_archive(archive_path, extract_to):
    """Extract ZIP or RAR archive"""
    try:
        if archive_path.lower().endswith('.zip'):
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
        elif archive_path.lower().endswith('.rar'):
            # For RAR support (requires rarfile and WinRAR/unrar)
            import rarfile
            with rarfile.RarFile(archive_path, 'r') as rar_ref:
                rar_ref.extractall(extract_to)
        return True
    except Exception as e:
        print(f"Archive extraction error: {e}")
        return False

def remove_background(image_path, output_path, model_name='u2net'):
    """Remove background from image using AI"""
    try:
        # Create a new rembg session with specified model
        session = new_session(model_name)
        
        with open(image_path, 'rb') as input_file:
            input_data = input_file.read()
        
        # Remove background
        output_data = remove(input_data, session=session)
        
        # Save the result
        with open(output_path, 'wb') as output_file:
            output_file.write(output_data)
        
        return True
    except Exception as e:
        print(f"Background removal error: {e}")
        return False

def convert_image_to_format(image_path, output_path, output_format='webp', quality=None, resize_option=None):
    """Convert image to specified format with optimization"""
    try:
        # Get quality settings based on format
        if output_format.lower() == 'webp':
            quality = quality or app.config.get('WEBP_QUALITY', 80)
        else:
            quality = quality or app.config.get('QUALITY_SETTINGS', {}).get('high', 85)
        
        with Image.open(image_path) as img:
            # Handle transparency for different formats
            if output_format.lower() in ['jpg', 'jpeg']:
                # JPEG doesn't support transparency, convert to RGB
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background for transparent images
                    bg = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    bg.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = bg
            elif output_format.lower() in ['png', 'webp', 'tiff', 'bmp']:
                # These formats support transparency
                if img.mode == 'P':
                    img = img.convert('RGBA')
            else:
                # For other formats, convert to RGB
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
            
            # Apply resize if specified
            if resize_option and resize_option != 'original':
                resize_dimensions = app.config.get('IMAGE_RESIZE_OPTIONS', {}).get(resize_option)
                if resize_dimensions:
                    img.thumbnail(resize_dimensions, Image.Resampling.LANCZOS)
            
            # Save with appropriate format and settings
            save_kwargs = {'optimize': True}
            
            if output_format.lower() == 'webp':
                save_kwargs['quality'] = quality
                img.save(output_path, 'WebP', **save_kwargs)
            elif output_format.lower() in ['jpg', 'jpeg']:
                save_kwargs['quality'] = quality
                img.save(output_path, 'JPEG', **save_kwargs)
            elif output_format.lower() == 'png':
                # PNG uses compression level instead of quality
                save_kwargs['compress_level'] = 6
                img.save(output_path, 'PNG', **save_kwargs)
            elif output_format.lower() == 'tiff':
                save_kwargs['quality'] = quality
                img.save(output_path, 'TIFF', **save_kwargs)
            elif output_format.lower() == 'bmp':
                img.save(output_path, 'BMP')
            else:
                # Default fallback
                img.save(output_path, output_format.upper(), **save_kwargs)
            
        return True
    except Exception as e:
        print(f"Image conversion error: {e}")
        return False

def process_images_in_folder(folder_path, output_folder, output_format='webp', quality=None, resize_option=None):
    """Process all images in folder and convert to specified format"""
    processed_count = 0
    os.makedirs(output_folder, exist_ok=True)
    
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if is_allowed_image(file):
                image_path = os.path.join(root, file)
                base_name = os.path.splitext(file)[0]
                sanitized_name = sanitize_filename(base_name)
                
                # Get proper file extension for output format
                if output_format.lower() in ['jpg', 'jpeg']:
                    ext = 'jpg'
                else:
                    ext = output_format.lower()
                
                output_filename = f"{sanitized_name}.{ext}"
                output_path = os.path.join(output_folder, output_filename)
                
                if convert_image_to_format(image_path, output_path, output_format, quality, resize_option):
                    processed_count += 1
    
    return processed_count

def create_zip_archive(folder_path, zip_path):
    """Create ZIP archive from folder"""
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, folder_path)
                    zipf.write(file_path, arcname)
        return True
    except Exception as e:
        print(f"ZIP creation error: {e}")
        return False

def cleanup_files(file_paths, delay=0):
    """Delete files with optional delay (background thread)"""
    def delete_files():
        if delay > 0:
            time.sleep(delay)
        
        for file_path in file_paths:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                print(f"Cleaned up: {file_path}")
            except Exception as e:
                print(f"Cleanup error for {file_path}: {e}")
    
    if delay > 0:
        cleanup_thread = threading.Thread(target=delete_files)
        cleanup_thread.daemon = True
        cleanup_thread.start()
    else:
        delete_files()

def get_or_create_upload_session():
    """Get or create upload session for guest/user tracking"""
    session_id = session.get('upload_session_id')
    
    if not session_id:
        session_id = str(uuid.uuid4())
        session['upload_session_id'] = session_id
    
    upload_session = UploadSession.query.filter_by(session_id=session_id).first()
    
    if not upload_session:
        user_id = current_user.id if current_user.is_authenticated else None
        upload_session = UploadSession(session_id=session_id, user_id=user_id)
        db.session.add(upload_session)
        db.session.commit()
    
    return upload_session

def check_upload_limit():
    """Check if user has reached upload limit"""
    upload_session = get_or_create_upload_session()
    
    if current_user.is_authenticated:
        plan_info = current_user.get_plan_info()
        max_uploads = plan_info.get('max_uploads', 50)
    else:
        max_uploads = app.config.get('GUEST_MAX_UPLOADS', 3)
    
    return upload_session.file_count < max_uploads

def get_remaining_uploads():
    """Get remaining upload count for current user/session"""
    upload_session = get_or_create_upload_session()
    
    if current_user.is_authenticated:
        plan_info = current_user.get_plan_info()
        max_uploads = plan_info.get('max_uploads', 50)
    else:
        max_uploads = app.config.get('GUEST_MAX_UPLOADS', 3)
    
    return max(0, max_uploads - upload_session.file_count)

# ERROR HANDLERS
@app.errorhandler(404)
def not_found(e):
    return render_template("404.html"), 404

@app.errorhandler(413)
def too_large(e):
    flash("File size too large! Maximum 50MB allowed.", "danger")
    return redirect(request.url)

# MAIN ROUTES
@app.route('/')
def index():
    """Home page - accessible to both guests and registered users"""
    remaining_uploads = get_remaining_uploads()
    upload_session = get_or_create_upload_session()
    
    context = {
        'remaining_uploads': remaining_uploads,
        'total_uploads': upload_session.file_count,
        'is_guest': not current_user.is_authenticated,
        'max_guest_uploads': app.config.get('MAX_GUEST_UPLOADS', 3),
        'max_registered_uploads': app.config.get('MAX_REGISTERED_UPLOADS', 20),
        'supported_formats': app.config.get('SUPPORTED_OUTPUT_FORMATS', ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff']),
        'quality_settings': app.config.get('QUALITY_SETTINGS', {}),
        'resize_options': app.config.get('IMAGE_RESIZE_OPTIONS', {})
    }
    
    return render_template("index.html", **context)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and bcrypt.check_password_hash(user.password, password):
            login_user(user)
            flash(f"Welcome back, {username}!", "success")
            
            # Update upload session with user ID
            upload_session = get_or_create_upload_session()
            upload_session.user_id = user.id
            db.session.commit()
            
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash("Invalid username or password", "danger")
    
    return render_template("login.html")

@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash("Username already exists! Please choose another.", "danger")
            return redirect(url_for('register'))
        
        # Create new user
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
        new_user = User(username=username, password=hashed_password)
        
        db.session.add(new_user)
        db.session.commit()
        
        flash("Registration successful! Please log in.", "success")
        return redirect(url_for('login'))
    
    return render_template("register.html")

@app.route('/upload', methods=['POST'])
def upload_files():
    """Main upload handler - processes ZIP/RAR archives"""
    
    # Check upload limit
    if not check_upload_limit():
        remaining = get_remaining_uploads()
        if current_user.is_authenticated:
            flash(f"Upload limit reached! You have {remaining} uploads remaining.", "warning")
        else:
            flash("Guest upload limit reached! Please register for more uploads.", "warning")
        return redirect(url_for('index'))
    
    uploaded_files = request.files.getlist('archives')
    output_folder_name = request.form.get('output_folder_name', '').strip()
    
    # Get conversion settings from form
    output_format = request.form.get('output_format', app.config.get('DEFAULT_OUTPUT_FORMAT', 'webp')).lower()
    quality_setting = request.form.get('quality', 'high')
    resize_option = request.form.get('resize_option', 'original')
    
    # Validate output format
    supported_formats = app.config.get('SUPPORTED_OUTPUT_FORMATS', ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff'])
    if output_format not in [fmt.lower() for fmt in supported_formats]:
        flash(f"Unsupported output format: {output_format}. Using WebP instead.", "warning")
        output_format = 'webp'
    
    # Get quality value
    quality_settings = app.config.get('QUALITY_SETTINGS', {})
    quality = quality_settings.get(quality_setting, 85)
    
    if not uploaded_files or all(file.filename == '' for file in uploaded_files):
        flash("No files selected! Please choose ZIP or RAR archives to upload.", "danger")
        return redirect(url_for('index'))
    
    # Process each uploaded archive
    processed_archives = []
    total_images_converted = 0
    errors = []
    
    for file in uploaded_files:
        if file.filename == '':
            continue
        
        if not is_allowed_archive(file.filename):
            errors.append(f"File '{file.filename}' is not a supported format. Only ZIP and RAR are allowed.")
            continue
        
        # Save uploaded archive
        unique_id = str(uuid.uuid4())
        archive_filename = secure_filename(file.filename)
        archive_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{unique_id}_{archive_filename}")
        
        try:
            file.save(archive_path)
        except Exception as e:
            errors.append(f"Error saving '{file.filename}': {str(e)}")
            continue
        
        # Extract archive
        extract_folder = os.path.join(app.config['UPLOAD_FOLDER'], f"extracted_{unique_id}")
        
        if extract_archive(archive_path, extract_folder):
            # Process images in extracted folder
            if not output_folder_name:
                output_folder_name = sanitize_filename(os.path.splitext(archive_filename)[0])
            
            output_folder = os.path.join(app.config['COMPRESSED_FOLDER'], f"{output_folder_name}_{unique_id}")
            converted_count = process_images_in_folder(extract_folder, output_folder, output_format, quality, resize_option)
            
            if converted_count > 0:
                # Create ZIP of processed images with format-specific naming
                format_ext = 'jpg' if output_format.lower() in ['jpg', 'jpeg'] else output_format.lower()
                zip_filename = f"{output_folder_name}_{unique_id}_{format_ext}.zip"
                zip_path = os.path.join(app.config['COMPRESSED_FOLDER'], zip_filename)
                
                if create_zip_archive(output_folder, zip_path):
                    processed_archives.append({
                        'zip_filename': zip_filename,
                        'original_name': archive_filename,
                        'images_count': converted_count,
                        'cleanup_paths': [archive_path, extract_folder, output_folder]
                    })
                    total_images_converted += converted_count
                else:
                    errors.append(f"Error creating output ZIP for '{archive_filename}'")
                    cleanup_files([archive_path, extract_folder, output_folder])
            else:
                errors.append(f"No images found in '{archive_filename}'")
                cleanup_files([archive_path, extract_folder])
        else:
            errors.append(f"Error extracting '{archive_filename}'. File may be corrupted.")
            cleanup_files([archive_path])
    
    # Show results
    if errors:
        for error in errors:
            flash(error, "warning")
    
    if processed_archives:
        # Update upload session
        upload_session = get_or_create_upload_session()
        upload_session.file_count += len(processed_archives)
        upload_session.last_upload = datetime.now(timezone.utc)
        db.session.commit()
        
        flash(f"Successfully processed {len(processed_archives)} archive(s) with {total_images_converted} images converted to {output_format.upper()}!", "success")
        
        # Store processed archives in session for download
        session['processed_archives'] = [
            {
                'zip_filename': arc['zip_filename'],
                'original_name': arc['original_name'],
                'images_count': arc['images_count']
            } for arc in processed_archives
        ]
        
        # Schedule cleanup of temporary files
        cleanup_paths = []
        for arc in processed_archives:
            cleanup_paths.extend(arc['cleanup_paths'])
        
        # Auto cleanup after configured time (default 30 minutes)
        auto_delete_minutes = app.config.get('AUTO_DELETE_AFTER', 30)
        cleanup_files(cleanup_paths, delay=auto_delete_minutes * 60)
        
        return redirect(url_for('download_page'))
    else:
        flash("No archives were successfully processed!", "danger")
        return redirect(url_for('index'))

@app.route('/download')
def download_page():
    """Download page showing processed files"""
    processed_archives = session.get('processed_archives', [])
    
    if not processed_archives:
        flash("No processed files available for download.", "info")
        return redirect(url_for('index'))
    
    return render_template("download.html", archives=processed_archives)

@app.route('/download/<filename>')
def download_file(filename):
    """Download individual file with auto-cleanup"""
    
    @after_this_request
    def cleanup_after_download(response):
        """Clean up file after download completes"""
        file_path = os.path.join(app.config['COMPRESSED_FOLDER'], filename)
        cleanup_files([file_path], delay=10)  # 10 second delay
        return response
    
    try:
        return send_from_directory(
            app.config['COMPRESSED_FOLDER'], 
            filename, 
            as_attachment=True,
            download_name=filename
        )
    except FileNotFoundError:
        flash("File not found or already downloaded!", "danger")
        return redirect(url_for('index'))

@app.route('/download-all')
def download_all():
    """Download all processed files as single ZIP"""
    processed_archives = session.get('processed_archives', [])
    
    if not processed_archives:
        flash("No files available for download.", "danger")
        return redirect(url_for('index'))
    
    # Create master ZIP containing all processed archives
    master_zip_name = f"all_converted_{str(uuid.uuid4())[:8]}.zip"
    master_zip_path = os.path.join(app.config['COMPRESSED_FOLDER'], master_zip_name)
    
    try:
        with zipfile.ZipFile(master_zip_path, 'w', zipfile.ZIP_DEFLATED) as master_zip:
            for archive in processed_archives:
                archive_path = os.path.join(app.config['COMPRESSED_FOLDER'], archive['zip_filename'])
                if os.path.exists(archive_path):
                    # Use original name for better organization
                    # Extract format from zip filename
                    base_name = os.path.splitext(archive['original_name'])[0]
                    zip_parts = archive['zip_filename'].split('_')
                    format_ext = zip_parts[-1].replace('.zip', '') if len(zip_parts) > 1 else 'converted'
                    display_name = f"{base_name}_{format_ext}.zip"
                    master_zip.write(archive_path, display_name)
        
        # Clear session data
        session.pop('processed_archives', None)
        
        @after_this_request
        def cleanup_after_download(response):
            # Delete individual archives and master ZIP after download
            cleanup_paths = [master_zip_path]
            for archive in processed_archives:
                cleanup_paths.append(os.path.join(app.config['COMPRESSED_FOLDER'], archive['zip_filename']))
            cleanup_files(cleanup_paths, delay=10)
            return response
        
        return send_from_directory(
            app.config['COMPRESSED_FOLDER'], 
            master_zip_name, 
            as_attachment=True,
            download_name=master_zip_name
        )
    
    except Exception as e:
        flash("Error creating download archive! Please try downloading files individually.", "danger")
        print(f"Download all error: {e}")
        return redirect(url_for('download_page'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Dashboard for registered users"""
    upload_session = get_or_create_upload_session()
    
    stats = {
        'username': current_user.username,
        'total_uploads': upload_session.file_count,
        'remaining_uploads': get_remaining_uploads(),
        'last_upload': upload_session.last_upload,
        'member_since': current_user.created_at,
        'is_premium': current_user.is_premium,
        'max_uploads': app.config.get('MAX_REGISTERED_UPLOADS', 20)
    }
    
    return render_template("dashboard.html", stats=stats)

@app.route('/logout')
@login_required
def logout():
    """User logout - keeps upload session for continuity"""
    username = current_user.username
    logout_user()
    flash(f"Goodbye {username}! Your upload session continues as guest.", "info")
    return redirect(url_for('index'))

@app.route('/reset-session')
def reset_session():
    """Reset upload session (for testing/demo purposes)"""
    session.pop('upload_session_id', None)
    session.pop('processed_archives', None)
    flash("Upload session reset successfully!", "info")
    return redirect(url_for('index'))

@app.route('/single-image', methods=['GET', 'POST'])
def single_image_convert():
    """Single image conversion"""
    if request.method == 'POST':
        # Check upload limit
        if not check_upload_limit():
            flash("Upload limit reached! Please register for more uploads or reset your session.", "warning")
            return redirect(url_for('single_image_convert'))
        
        uploaded_file = request.files.get('single_image')
        output_format = request.form.get('output_format', 'webp').lower()
        quality_setting = request.form.get('quality', 'high')
        resize_option = request.form.get('resize_option', 'original')
        
        if not uploaded_file or uploaded_file.filename == '':
            flash("No image selected! Please choose an image file.", "danger")
            return redirect(url_for('single_image_convert'))
        
        if not is_allowed_image(uploaded_file.filename):
            flash("Invalid image format! Please upload a valid image file.", "danger")
            return redirect(url_for('single_image_convert'))
        
        # Validate format and get quality
        supported_formats = app.config.get('SUPPORTED_OUTPUT_FORMATS', ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff'])
        if output_format not in [fmt.lower() for fmt in supported_formats]:
            output_format = 'webp'
        
        quality_settings = app.config.get('QUALITY_SETTINGS', {})
        quality = quality_settings.get(quality_setting, 85)
        
        # Save uploaded image
        unique_id = str(uuid.uuid4())
        original_filename = secure_filename(uploaded_file.filename)
        input_path = os.path.join(app.config['SINGLE_IMAGE_FOLDER'], f"{unique_id}_{original_filename}")
        
        try:
            os.makedirs(app.config['SINGLE_IMAGE_FOLDER'], exist_ok=True)
            uploaded_file.save(input_path)
        except Exception as e:
            flash(f"Error saving image: {str(e)}", "danger")
            return redirect(url_for('single_image_convert'))
        
        # Convert image
        base_name = os.path.splitext(original_filename)[0]
        sanitized_name = sanitize_filename(base_name)
        
        # Get proper file extension
        if output_format.lower() in ['jpg', 'jpeg']:
            ext = 'jpg'
        else:
            ext = output_format.lower()
        
        output_filename = f"{sanitized_name}_converted.{ext}"
        output_path = os.path.join(app.config['SINGLE_IMAGE_FOLDER'], output_filename)
        
        if convert_image_to_format(input_path, output_path, output_format, quality, resize_option):
            # Update upload session
            upload_session = get_or_create_upload_session()
            upload_session.file_count += 1
            upload_session.last_upload = datetime.now(timezone.utc)
            db.session.commit()
            
            # Store conversion info in session
            session['single_converted'] = {
                'filename': output_filename,
                'original_name': original_filename,
                'format': output_format.upper(),
                'cleanup_path': input_path
            }
            
            flash(f"Image successfully converted to {output_format.upper()}!", "success")
            return redirect(url_for('download_single'))
        else:
            cleanup_files([input_path])
            flash("Error converting image! Please try again with a different file.", "danger")
            return redirect(url_for('single_image_convert'))
    
    # GET request - show upload form
    remaining_uploads = get_remaining_uploads()
    
    context = {
        'remaining_uploads': remaining_uploads,
        'is_guest': not current_user.is_authenticated,
        'supported_formats': app.config.get('SUPPORTED_OUTPUT_FORMATS', ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff']),
        'quality_settings': app.config.get('QUALITY_SETTINGS', {}),
        'resize_options': app.config.get('IMAGE_RESIZE_OPTIONS', {})
    }
    
    return render_template("single_image.html", **context)

@app.route('/download-single')
def download_single():
    """Download single converted image"""
    converted_info = session.get('single_converted')
    
    if not converted_info:
        flash("No converted image available for download.", "info")
        return redirect(url_for('single_image_convert'))
    
    @after_this_request
    def cleanup_after_download(response):
        """Clean up files after download"""
        single_image_path = os.path.join(app.config['SINGLE_IMAGE_FOLDER'], converted_info['filename'])
        cleanup_paths = [single_image_path, converted_info['cleanup_path']]
        cleanup_files(cleanup_paths, delay=10)
        session.pop('single_converted', None)
        return response
    
    try:
        return send_from_directory(
            app.config['SINGLE_IMAGE_FOLDER'], 
            converted_info['filename'], 
            as_attachment=True,
            download_name=converted_info['filename']
        )
    except FileNotFoundError:
        flash("File not found or already downloaded!", "danger")
        return redirect(url_for('single_image_convert'))

@app.route('/bg-remover', methods=['GET', 'POST'])
def bg_remover():
    """AI Background Removal Tool"""
    if request.method == 'POST':
        # Check if user has bg_removal feature
        if current_user.is_authenticated:
            if not current_user.has_feature('bg_removal'):
                flash("Background removal is not available in your plan. Upgrade to access this feature!", "warning")
                return redirect(url_for('bg_remover'))
        else:
            flash("Background removal requires registration. Please sign up for free access!", "warning")
            return redirect(url_for('register'))
        
        # Check upload limit
        if not check_upload_limit():
            flash("Upload limit reached! Please register for more uploads or reset your session.", "warning")
            return redirect(url_for('bg_remover'))
        
        uploaded_file = request.files.get('bg_image')
        bg_model = request.form.get('bg_model', app.config.get('DEFAULT_BG_MODEL', 'u2net'))
        output_format = request.form.get('output_format', 'png').lower()
        
        if not uploaded_file or uploaded_file.filename == '':
            flash("No image selected! Please choose an image file.", "danger")
            return redirect(url_for('bg_remover'))
        
        if not is_allowed_image(uploaded_file.filename):
            flash("Invalid image format! Please upload a valid image file.", "danger")
            return redirect(url_for('bg_remover'))
        
        # Save uploaded image
        unique_id = str(uuid.uuid4())
        original_filename = secure_filename(uploaded_file.filename)
        input_path = os.path.join(app.config['BG_REMOVED_FOLDER'], f"{unique_id}_{original_filename}")
        
        try:
            os.makedirs(app.config['BG_REMOVED_FOLDER'], exist_ok=True)
            uploaded_file.save(input_path)
        except Exception as e:
            flash(f"Error saving image: {str(e)}", "danger")
            return redirect(url_for('bg_remover'))
        
        # Remove background
        base_name = os.path.splitext(original_filename)[0]
        sanitized_name = sanitize_filename(base_name)
        output_filename = f"{sanitized_name}_nobg.{output_format}"
        output_path = os.path.join(app.config['BG_REMOVED_FOLDER'], output_filename)
        
        if remove_background(input_path, output_path, bg_model):
            # Update upload session
            upload_session = get_or_create_upload_session()
            upload_session.file_count += 1
            upload_session.last_upload = datetime.now(timezone.utc)
            db.session.commit()
            
            # Store conversion info in session
            session['bg_removed'] = {
                'filename': output_filename,
                'original_name': original_filename,
                'model_used': bg_model,
                'cleanup_path': input_path
            }
            
            flash(f"Background successfully removed using {bg_model} model!", "success")
            return redirect(url_for('download_bg_removed'))
        else:
            cleanup_files([input_path])
            flash("Error removing background! Please try again with a different image or model.", "danger")
            return redirect(url_for('bg_remover'))
    
    # GET request - show upload form
    remaining_uploads = get_remaining_uploads()
    
    context = {
        'remaining_uploads': remaining_uploads,
        'is_guest': not current_user.is_authenticated,
        'bg_models': app.config.get('BG_REMOVAL_MODELS', {}),
        'supported_formats': ['png', 'webp', 'jpg'],
        'has_bg_feature': current_user.has_feature('bg_removal') if current_user.is_authenticated else False
    }
    
    return render_template("bg_remover.html", **context)

@app.route('/download-bg-removed')
def download_bg_removed():
    """Download background removed image"""
    bg_info = session.get('bg_removed')
    
    if not bg_info:
        flash("No processed image available for download.", "info")
        return redirect(url_for('bg_remover'))
    
    @after_this_request
    def cleanup_after_download(response):
        """Clean up files after download"""
        bg_image_path = os.path.join(app.config['BG_REMOVED_FOLDER'], bg_info['filename'])
        cleanup_paths = [bg_image_path, bg_info['cleanup_path']]
        cleanup_files(cleanup_paths, delay=10)
        session.pop('bg_removed', None)
        return response
    
    try:
        return send_from_directory(
            app.config['BG_REMOVED_FOLDER'], 
            bg_info['filename'], 
            as_attachment=True,
            download_name=bg_info['filename']
        )
    except FileNotFoundError:
        flash("File not found or already downloaded!", "danger")
        return redirect(url_for('bg_remover'))

@app.route('/batch-images', methods=['GET', 'POST'])
def batch_images():
    """Multiple Image Upload and Conversion"""
    if request.method == 'POST':
        # Check if user has batch_upload feature
        if current_user.is_authenticated:
            if not current_user.has_feature('batch_upload'):
                flash("Batch upload is not available in your plan. Upgrade to access this feature!", "warning")
                return redirect(url_for('batch_images'))
            max_batch = current_user.get_plan_info().get('max_batch_images', 10)
        else:
            max_batch = app.config.get('GUEST_MAX_BATCH_IMAGES', 3)
        
        # Check upload limit
        if not check_upload_limit():
            flash("Upload limit reached! Please register for more uploads or reset your session.", "warning")
            return redirect(url_for('batch_images'))
        
        uploaded_files = request.files.getlist('batch_images')
        output_format = request.form.get('output_format', 'webp').lower()
        quality_setting = request.form.get('quality', 'high')
        resize_option = request.form.get('resize_option', 'original')
        remove_bg = request.form.get('remove_bg') == 'on'
        bg_model = request.form.get('bg_model', 'u2net') if remove_bg else None
        
        if not uploaded_files or all(file.filename == '' for file in uploaded_files):
            flash("No images selected! Please choose image files.", "danger")
            return redirect(url_for('batch_images'))
        
        # Filter valid images and check limit
        valid_files = [f for f in uploaded_files if f.filename != '' and is_allowed_image(f.filename)]
        
        if len(valid_files) > max_batch:
            flash(f"Too many images! Maximum {max_batch} images allowed in your plan.", "warning")
            return redirect(url_for('batch_images'))
        
        if not valid_files:
            flash("No valid image files found! Please upload valid image formats.", "danger")
            return redirect(url_for('batch_images'))
        
        # Process images
        processed_images = []
        errors = []
        unique_id = str(uuid.uuid4())
        
        # Create batch folder
        batch_folder = os.path.join(app.config['BATCH_IMAGES_FOLDER'], f"batch_{unique_id}")
        os.makedirs(batch_folder, exist_ok=True)
        
        quality_settings = app.config.get('QUALITY_SETTINGS', {})
        quality = quality_settings.get(quality_setting, 85)
        
        for file in valid_files:
            try:
                # Save original file
                original_filename = secure_filename(file.filename)
                input_path = os.path.join(batch_folder, f"original_{original_filename}")
                file.save(input_path)
                
                base_name = os.path.splitext(original_filename)[0]
                sanitized_name = sanitize_filename(base_name)
                
                # Process image
                if remove_bg and current_user.is_authenticated and current_user.has_feature('bg_removal'):
                    # Remove background first
                    bg_removed_path = os.path.join(batch_folder, f"nobg_{sanitized_name}.png")
                    if remove_background(input_path, bg_removed_path, bg_model):
                        process_path = bg_removed_path
                    else:
                        errors.append(f"Failed to remove background from {original_filename}")
                        continue
                else:
                    process_path = input_path
                
                # Convert format
                ext = 'jpg' if output_format.lower() in ['jpg', 'jpeg'] else output_format.lower()
                output_filename = f"{sanitized_name}_converted.{ext}"
                output_path = os.path.join(batch_folder, output_filename)
                
                if convert_image_to_format(process_path, output_path, output_format, quality, resize_option):
                    processed_images.append({
                        'original_name': original_filename,
                        'converted_name': output_filename,
                        'size': os.path.getsize(output_path) if os.path.exists(output_path) else 0
                    })
                else:
                    errors.append(f"Failed to convert {original_filename}")
                    
            except Exception as e:
                errors.append(f"Error processing {file.filename}: {str(e)}")
        
        if errors:
            for error in errors:
                flash(error, "warning")
        
        if processed_images:
            # Create ZIP of processed images
            zip_filename = f"batch_converted_{unique_id}.zip"
            zip_path = os.path.join(app.config['BATCH_IMAGES_FOLDER'], zip_filename)
            
            if create_zip_archive(batch_folder, zip_path):
                # Update upload session
                upload_session = get_or_create_upload_session()
                upload_session.file_count += len(processed_images)
                upload_session.last_upload = datetime.now(timezone.utc)
                db.session.commit()
                
                # Store batch info in session
                session['batch_processed'] = {
                    'zip_filename': zip_filename,
                    'images': processed_images,
                    'format': output_format.upper(),
                    'with_bg_removal': remove_bg,
                    'cleanup_path': batch_folder
                }
                
                success_msg = f"Successfully processed {len(processed_images)} images"
                if remove_bg:
                    success_msg += " with background removal"
                success_msg += f" to {output_format.upper()}!"
                
                flash(success_msg, "success")
                return redirect(url_for('download_batch'))
            else:
                cleanup_files([batch_folder])
                flash("Error creating download archive! Please try again.", "danger")
        else:
            cleanup_files([batch_folder])
            flash("No images were successfully processed!", "danger")
        
        return redirect(url_for('batch_images'))
    
    # GET request - show upload form
    remaining_uploads = get_remaining_uploads()
    
    if current_user.is_authenticated:
        plan_info = current_user.get_plan_info()
        max_batch = plan_info.get('max_batch_images', 10)
        has_batch_feature = current_user.has_feature('batch_upload')
        has_bg_feature = current_user.has_feature('bg_removal')
    else:
        max_batch = app.config.get('GUEST_MAX_BATCH_IMAGES', 3)
        has_batch_feature = False
        has_bg_feature = False
    
    context = {
        'remaining_uploads': remaining_uploads,
        'max_batch_images': max_batch,
        'is_guest': not current_user.is_authenticated,
        'supported_formats': app.config.get('SUPPORTED_OUTPUT_FORMATS', ['webp', 'png', 'jpg', 'jpeg', 'bmp', 'tiff']),
        'quality_settings': app.config.get('QUALITY_SETTINGS', {}),
        'resize_options': app.config.get('IMAGE_RESIZE_OPTIONS', {}),
        'bg_models': app.config.get('BG_REMOVAL_MODELS', {}),
        'has_batch_feature': has_batch_feature,
        'has_bg_feature': has_bg_feature
    }
    
    return render_template("batch_images.html", **context)

@app.route('/download-batch')
def download_batch():
    """Download batch processed images"""
    batch_info = session.get('batch_processed')
    
    if not batch_info:
        flash("No processed images available for download.", "info")
        return redirect(url_for('batch_images'))
    
    @after_this_request
    def cleanup_after_download(response):
        """Clean up files after download"""
        zip_path = os.path.join(app.config['BATCH_IMAGES_FOLDER'], batch_info['zip_filename'])
        cleanup_paths = [zip_path, batch_info['cleanup_path']]
        cleanup_files(cleanup_paths, delay=10)
        session.pop('batch_processed', None)
        return response
    
    try:
        return send_from_directory(
            app.config['BATCH_IMAGES_FOLDER'], 
            batch_info['zip_filename'], 
            as_attachment=True,
            download_name=batch_info['zip_filename']
        )
    except FileNotFoundError:
        flash("File not found or already downloaded!", "danger")
        return redirect(url_for('batch_images'))

@app.route('/api/upload-status')
def upload_status():
    """API endpoint for upload status (for AJAX)"""
    upload_session = get_or_create_upload_session()
    remaining = get_remaining_uploads()
    
    return {
        'remaining_uploads': remaining,
        'total_uploads': upload_session.file_count,
        'is_authenticated': current_user.is_authenticated,
        'max_uploads': app.config.get('MAX_REGISTERED_UPLOADS', 20) if current_user.is_authenticated else app.config.get('MAX_GUEST_UPLOADS', 3)
    }

# INITIALIZATION
if __name__ == "__main__":
    # Create necessary directories
    os.makedirs('instance', exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['COMPRESSED_FOLDER'], exist_ok=True)
    os.makedirs(app.config['SINGLE_IMAGE_FOLDER'], exist_ok=True)
    os.makedirs(app.config['BATCH_IMAGES_FOLDER'], exist_ok=True)
    os.makedirs(app.config['BG_REMOVED_FOLDER'], exist_ok=True)
    
    # Initialize database with migration
    with app.app_context():
        try:
            # Try to create all tables
            db.create_all()
            
            # Check if 'plan' column exists in User table, if not add it
            inspector = inspect(db.engine)
            user_columns = [col['name'] for col in inspector.get_columns('user')]
            
            if 'plan' not in user_columns:
                print("🔄 Adding missing 'plan' column to User table...")
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE user ADD COLUMN plan VARCHAR(50) DEFAULT 'basic'"))
                    conn.commit()
                print("✅ Database migration completed successfully!")
            
            print("✅ Database tables created successfully!")
            
        except Exception as e:
            print(f"⚠️ Database initialization issue: {e}")
            print("💡 Trying to recreate database schema...")
            try:
                # Drop and recreate all tables as fallback
                db.drop_all()
                db.create_all()
                print("✅ Database recreated successfully!")
            except Exception as e2:
                print(f"❌ Database error: {e2}")
                print("🔧 Please manually delete database files in instance/ folder and restart")
    
    # Startup messages
    print("\n" + "="*60)
    print("🚀 IMAGE CONVERTER WEB APP - STARTING")
    print("="*60)
    print("✅ Features Enabled:")
    print("   📤 Guest Upload: Max 3 archives")
    print("   👤 Registered Users: Max 20 archives")
    print("   📦 Archive Support: ZIP & RAR")
    print("   🖼️  WebP Conversion: High quality")
    print("   🗑️  Auto Cleanup: After download")
    print("   🧹 File Sanitization: Special chars removed")
    print("   📁 Custom Folder Names: User configurable")
    print("="*60)
    print("🌐 Server URL: http://127.0.0.1:5000")
    print("🔧 Debug Mode: Enabled")
    print("="*60)
    
    # Start Flask development server
    app.run(debug=True, port=5000, host='127.0.0.1')
