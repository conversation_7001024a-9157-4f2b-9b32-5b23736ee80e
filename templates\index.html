{% extends "layout.html" %}
{% block title %}Image Converter - Convert Archives to WebP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center bg-gradient p-5 rounded-4 text-white mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h1 class="display-4 fw-bold mb-3">🖼️ Image Converter Pro</h1>
                <p class="lead mb-4">Upload ZIP/RAR archives and convert all images to optimized WebP format</p>
                
                <!-- User Status -->
                {% if current_user.is_authenticated %}
                    <div class="alert alert-success d-inline-block">
                        <i class="fas fa-user-check"></i> Welcome back, <strong>{{ current_user.username }}</strong>! 
                        You have <strong>{{ remaining_uploads }}</strong> uploads remaining.
                    </div>
                {% else %}
                    <div class="alert alert-warning d-inline-block">
                        <i class="fas fa-guest"></i> Guest Mode: <strong>{{ remaining_uploads }}</strong> uploads remaining. 
                        <a href="{{ url_for('register') }}" class="text-dark fw-bold">Register</a> for more!
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Upload Section -->
    {% if remaining_uploads > 0 %}
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-cloud-upload-alt"></i> Upload Archives</h3>
                </div>
                <div class="card-body p-4">
                    <form action="{{ url_for('upload_files') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                        <!-- File Upload -->
                        <div class="mb-4">
                            <label for="archives" class="form-label fw-bold">Select ZIP or RAR Archives</label>
                            <input type="file" class="form-control form-control-lg" id="archives" name="archives" 
                                   multiple accept=".zip,.rar" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i> Supports: .zip, .rar files (Max 50MB each)
                            </div>
                        </div>

                        <!-- Output Format Selection -->
                        <div class="mb-4">
                            <label for="output_format" class="form-label fw-bold">Output Format</label>
                            <select class="form-select" id="output_format" name="output_format">
                                <option value="webp" selected>WebP (Best compression)</option>
                                <option value="png">PNG (Lossless with transparency)</option>
                                <option value="jpg">JPEG (Universal compatibility)</option>
                                <option value="bmp">BMP (Uncompressed)</option>
                                <option value="tiff">TIFF (High quality)</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-image"></i> Choose output image format
                            </div>
                        </div>

                        <!-- Quality Settings -->
                        <div class="mb-4">
                            <label for="quality" class="form-label fw-bold">Quality Level</label>
                            <select class="form-select" id="quality" name="quality">
                                <option value="medium">Medium (75%)</option>
                                <option value="high" selected>High (85%)</option>
                                <option value="ultra">Ultra (95%)</option>
                                <option value="low">Low (60%) - Smaller size</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-star"></i> Higher quality = larger file size
                            </div>
                        </div>

                        <!-- Resize Options -->
                        <div class="mb-4">
                            <label for="resize_option" class="form-label fw-bold">Image Size</label>
                            <select class="form-select" id="resize_option" name="resize_option">
                                <option value="original" selected>Original Size</option>
                                <option value="xl">Extra Large (1920px)</option>
                                <option value="large">Large (1200px)</option>
                                <option value="medium">Medium (800px)</option>
                                <option value="small">Small (400px)</option>
                                <option value="thumbnail">Thumbnail (150px)</option>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-expand-arrows-alt"></i> Resize images while maintaining aspect ratio
                            </div>
                        </div>

                        <!-- Output Folder Name -->
                        <div class="mb-4">
                            <label for="output_folder_name" class="form-label fw-bold">Output Folder Name (Optional)</label>
                            <input type="text" class="form-control" id="output_folder_name" name="output_folder_name" 
                                   placeholder="Leave empty to use archive name">
                            <div class="form-text">
                                <i class="fas fa-folder"></i> Custom name for your converted images folder
                            </div>
                        </div>

                        <!-- Upload Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn">
                                <i class="fas fa-magic"></i> <span id="convertText">Convert Images</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Upload Limit Reached -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="alert alert-danger text-center">
                <h4><i class="fas fa-exclamation-triangle"></i> Upload Limit Reached</h4>
                {% if current_user.is_authenticated %}
                    <p>You have used all {{ max_registered_uploads }} uploads. Contact support for more.</p>
                {% else %}
                    <p>Guest limit of {{ max_guest_uploads }} uploads reached.</p>
                    <a href="{{ url_for('register') }}" class="btn btn-success me-2">Register for {{ max_registered_uploads }} uploads</a>
                    <a href="{{ url_for('reset_session') }}" class="btn btn-outline-primary">Reset Session (Demo)</a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Features Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h2 class="text-center mb-4">✨ Features</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-primary mb-3"><i class="fas fa-archive fa-3x"></i></div>
                            <h5>Archive Support</h5>
                            <p class="text-muted">Upload ZIP & RAR archives containing your images</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-success mb-3"><i class="fas fa-exchange-alt fa-3x"></i></div>
                            <h5>Multi-Format Support</h5>
                            <p class="text-muted">Convert to WebP, PNG, JPEG, BMP, or TIFF formats</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-warning mb-3"><i class="fas fa-magic fa-3x"></i></div>
                            <h5>Auto Cleanup</h5>
                            <p class="text-muted">Files automatically deleted after download</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-info mb-3"><i class="fas fa-user-friends fa-3x"></i></div>
                            <h5>Guest Access</h5>
                            <p class="text-muted">No registration required for basic usage</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-danger mb-3"><i class="fas fa-file-signature fa-3x"></i></div>
                            <h5>File Sanitization</h5>
                            <p class="text-muted">Special characters removed from filenames</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-secondary mb-3"><i class="fas fa-download fa-3x"></i></div>
                            <h5>Multiple Downloads</h5>
                            <p class="text-muted">Download individually or all at once</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-primary mb-3"><i class="fas fa-sliders-h fa-3x"></i></div>
                            <h5>Quality & Resize</h5>
                            <p class="text-muted">Adjust quality levels and resize images as needed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-success mb-3"><i class="fas fa-palette fa-3x"></i></div>
                            <h5>Smart Transparency</h5>
                            <p class="text-muted">Handles transparent images perfectly across formats</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Limits Info -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5><i class="fas fa-info-circle text-primary"></i> Upload Limits</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Guest Users:</strong>
                            <ul>
                                <li>{{ max_guest_uploads }} archives per session</li>
                                <li>50MB max file size</li>
                                <li>No registration required</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong>Registered Users:</strong>
                            <ul>
                                <li>{{ max_registered_uploads }} archives per account</li>
                                <li>50MB max file size</li>
                                <li>Upload history tracking</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Progress Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Processing Your Archives...</h5>
                <p class="text-muted" id="modalText">Please wait while we extract and convert your images...</p>
            </div>
        </div>
    </div>
</div>

<script>
// Update button text and modal text based on selected format
function updateConvertButton() {
    const format = document.getElementById('output_format').value.toUpperCase();
    const convertText = document.getElementById('convertText');
    const modalText = document.getElementById('modalText');
    
    convertText.textContent = `Convert to ${format}`;
    modalText.textContent = `Please wait while we extract and convert your images to ${format} format...`;
}

// Event listeners
document.getElementById('output_format').addEventListener('change', updateConvertButton);

document.getElementById('uploadForm').addEventListener('submit', function() {
    updateConvertButton(); // Update modal text before showing
    var modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
});

// File input validation
document.getElementById('archives').addEventListener('change', function() {
    const files = this.files;
    const maxSize = 50 * 1024 * 1024; // 50MB
    let valid = true;
    
    for (let i = 0; i < files.length; i++) {
        if (files[i].size > maxSize) {
            alert(`File "${files[i].name}" is too large. Maximum size is 50MB.`);
            valid = false;
            break;
        }
    }
    
    if (!valid) {
        this.value = '';
    }
});

// Initialize button text on page load
document.addEventListener('DOMContentLoaded', function() {
    updateConvertButton();
});
</script>
{% endblock %}
