﻿{% extends "layout.html" %}
{% block title %}Single Image Converter - Convert Any Image Format{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center bg-gradient p-5 rounded-4 text-white mb-4" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); box-shadow: 0 20px 40px rgba(46,204,113,0.3);">
                <h1 class="display-4 fw-bold mb-3">🖼️ Single Image Converter</h1>
                <p class="lead mb-4">Upload any image and convert it to your preferred format instantly</p>
                
                <!-- User Status -->
                {% if current_user.is_authenticated %}
                    <div class="alert alert-success d-inline-block bg-white bg-opacity-20 border-0">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Registered User</strong> - Enhanced features available
                    </div>
                {% else %}
                    <div class="alert alert-info d-inline-block bg-white bg-opacity-20 border-0">
                        <i class="fas fa-user me-2"></i>
                        <strong>Guest Mode</strong> - Limited uploads, register for more
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Upload Section -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header bg-gradient text-white text-center py-4" 
                     style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);">
                    <h3 class="mb-0"><i class="fas fa-upload me-2"></i>Single Image Conversion</h3>
                </div>
                
                <div class="card-body p-5">
                    <form method="POST" enctype="multipart/form-data" id="singleImageForm" class="needs-validation" novalidate>
                        
                        <!-- File Upload Section -->
                        <div class="mb-4">
                            <label for="single_image" class="form-label fw-bold fs-5">
                                <i class="fas fa-image text-primary me-2"></i>Choose Image
                            </label>
                            <div class="upload-zone border-2 border-dashed border-primary rounded-4 p-4 text-center bg-light position-relative"
                                 style="min-height: 200px; transition: all 0.3s ease;">
                                <input type="file" class="form-control position-absolute w-100 h-100 opacity-0" 
                                       id="single_image" name="single_image" accept="image/*" required style="cursor: pointer;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5 class="text-primary">Drop your image here or click to browse</h5>
                                    <p class="text-muted mb-0">Supports: JPG, PNG, WebP, GIF, BMP, TIFF</p>
                                </div>
                            </div>
                        </div>

                        <!-- Output Format -->
                        <div class="mb-4">
                            <label for="output_format" class="form-label fw-bold">
                                <i class="fas fa-file-export text-warning me-2"></i>Output Format
                            </label>
                            <select class="form-select form-select-lg" id="output_format" name="output_format">
                                {% for format in supported_formats %}
                                <option value="{{ format }}" {% if format == 'webp' %}selected{% endif %}>
                                    {{ format.upper() }}{% if format == 'webp' %} (Recommended){% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Quality Setting -->
                        <div class="mb-4">
                            <label for="quality" class="form-label fw-bold">
                                <i class="fas fa-medal text-success me-2"></i>Quality Level
                            </label>
                            <select class="form-select form-select-lg" id="quality" name="quality">
                                {% for key, value in quality_settings.items() %}
                                <option value="{{ key }}" {% if key == 'high' %}selected{% endif %}>
                                    {{ key.title() }} ({{ value }}%)
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Resize Option -->
                        <div class="mb-4">
                            <label for="resize_option" class="form-label fw-bold">
                                <i class="fas fa-expand-arrows-alt text-info me-2"></i>Resize Image
                            </label>
                            <select class="form-select form-select-lg" id="resize_option" name="resize_option">
                                <option value="original" selected>Keep Original Size</option>
                                {% for key, value in resize_options.items() %}
                                <option value="{{ key }}">{{ key.title() }} ({{ value[0] }}x{{ value[1] }})</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Upload Stats -->
                        <div class="alert alert-info rounded-4 border-0 mb-4" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <strong class="text-primary">{{ remaining_uploads }}</strong><br>
                                    <small>Remaining Uploads</small>
                                </div>
                                <div class="col-md-4">
                                    <strong class="text-success">Instant</strong><br>
                                    <small>Processing</small>
                                </div>
                                <div class="col-md-4">
                                    <strong class="text-warning">High Quality</strong><br>
                                    <small>Conversion</small>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-lg py-3 rounded-4 fw-bold" 
                                    style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); border: none; color: white; box-shadow: 0 10px 30px rgba(46,204,113,0.3);"
                                    id="convertBtn">
                                <i class="fas fa-magic me-2"></i>Convert Image
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h2 class="fw-bold">⚡ Single Image Features</h2>
                <p class="lead text-muted">Professional image conversion with advanced options</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3"><i class="fas fa-exchange-alt fa-3x"></i></div>
                            <h5 class="fw-bold">Format Conversion</h5>
                            <p class="text-muted">Convert between all major image formats instantly</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center">
                        <div class="card-body p-4">
                            <div class="text-success mb-3"><i class="fas fa-compress-arrows-alt fa-3x"></i></div>
                            <h5 class="fw-bold">Smart Resizing</h5>
                            <p class="text-muted">Resize images to perfect dimensions with quality preservation</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm rounded-4 text-center">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3"><i class="fas fa-star fa-3x"></i></div>
                            <h5 class="fw-bold">Quality Control</h5>
                            <p class="text-muted">Fine-tune compression settings for optimal results</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 rounded-4">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                <h5 class="fw-bold mb-2">Converting Image...</h5>
                <p class="text-muted mb-0" id="modalText">Processing your image, please wait...</p>
            </div>
        </div>
    </div>
</div>

<style>
.upload-zone:hover {
    border-color: #2ecc71 !important;
    background-color: #f0fdf4 !important;
}
</style>

<script>
document.getElementById('singleImageForm').addEventListener('submit', function() {
    var modal = new bootstrap.Modal(document.getElementById('processingModal'));
    modal.show();
});

// File upload preview
document.getElementById('single_image').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const uploadContent = document.querySelector('.upload-content');
            uploadContent.innerHTML = `
                <img src="${e.target.result}" style="max-width: 100%; max-height: 150px; object-fit: contain;" class="mb-2 rounded">
                <h6 class="text-success"><i class="fas fa-check-circle me-1"></i>${file.name}</h6>
                <p class="text-muted mb-0">Ready for conversion</p>
            `;
        };
        reader.readAsDataURL(file);
    }
});

// Update convert button text based on format
document.getElementById('output_format').addEventListener('change', function() {
    const format = this.value.toUpperCase();
    const convertBtn = document.getElementById('convertBtn');
    convertBtn.innerHTML = `<i class="fas fa-magic me-2"></i>Convert to ${format}`;
});
</script>
{% endblock %}
